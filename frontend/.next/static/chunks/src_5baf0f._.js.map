{"version": 3, "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/src/components/RecommendationResult.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport {\n  MapPin,\n  Clock,\n  Utensils,\n  Bed,\n  Lightbulb,\n  ChevronDown,\n  ChevronUp,\n} from \"lucide-react\";\n\ninterface RecommendationResultProps {\n  data: {\n    recommendations: any;\n    analysis: string;\n    success: boolean;\n  };\n}\n\nexport default function RecommendationResult({\n  data,\n}: RecommendationResultProps) {\n  const [activeTab, setActiveTab] = useState(\"itinerary\");\n  const [expandedDay, setExpandedDay] = useState<number | null>(null);\n\n  if (!data.recommendations) {\n    return (\n      <div className=\"text-center text-red-500 p-8\">\n        <p>3推荐生成失败，请稍后重试</p>\n      </div>\n    );\n  }\n\n  const { recommendations, analysis } = data;\n\n  // 处理不同格式的推荐数据\n  const getRecommendationData = () => {\n    if (typeof recommendations === \"string\") {\n      return {\n        summary: recommendations,\n        itinerary: [],\n        restaurants: [],\n        attractions: [],\n        accommodations: [],\n        tips: [],\n      };\n    }\n\n    return {\n      summary: recommendations.summary || \"\",\n      itinerary: recommendations.itinerary || [],\n      restaurants: recommendations.restaurants || [],\n      attractions: recommendations.attractions || [],\n      accommodations: recommendations.accommodations || [],\n      tips: recommendations.tips || [],\n    };\n  };\n\n  const recData = getRecommendationData();\n\n  const tabs = [\n    { id: \"itinerary\", label: \"行程安排\", icon: Clock },\n    { id: \"attractions\", label: \"推荐景点\", icon: MapPin },\n    { id: \"restaurants\", label: \"美食推荐\", icon: Utensils },\n    { id: \"accommodations\", label: \"住宿建议\", icon: Bed },\n    { id: \"tips\", label: \"旅行贴士\", icon: Lightbulb },\n  ];\n\n  const renderItinerary = () => {\n    if (!recData.itinerary.length) {\n      return (\n        <div className=\"text-gray-500 text-center py-8\">\n          <p>暂无详细行程安排</p>\n          {recData.summary && (\n            <div className=\"mt-4 p-4 bg-gray-50 rounded-lg text-left\">\n              <p className=\"whitespace-pre-wrap\">{recData.summary}</p>\n            </div>\n          )}\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"space-y-4\">\n        {recData.itinerary.map((day: any, index: number) => (\n          <div key={index} className=\"border border-gray-200 rounded-lg\">\n            <button\n              onClick={() =>\n                setExpandedDay(expandedDay === index ? null : index)\n              }\n              className=\"w-full p-4 text-left flex items-center justify-between hover:bg-gray-50\"\n            >\n              <div>\n                <h3 className=\"font-semibold text-lg\">第 {index + 1} 天</h3>\n                <p className=\"text-gray-600\">{day.date}</p>\n              </div>\n              {expandedDay === index ? <ChevronUp /> : <ChevronDown />}\n            </button>\n\n            {expandedDay === index && (\n              <div className=\"px-4 pb-4 border-t border-gray-100\">\n                <div className=\"space-y-3 mt-3\">\n                  {day.activities?.map((activity: any, actIndex: number) => (\n                    <div key={actIndex} className=\"flex items-start space-x-3\">\n                      <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n                      <div>\n                        <p className=\"font-medium\">\n                          {activity.time || `活动 ${actIndex + 1}`}\n                        </p>\n                        <p className=\"text-gray-600\">\n                          {activity.description || activity}\n                        </p>\n                      </div>\n                    </div>\n                  )) || (\n                    <p className=\"text-gray-600\">{day.description || day}</p>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n    );\n  };\n\n  const renderList = (items: any[], emptyMessage: string) => {\n    if (!items.length) {\n      return <p className=\"text-gray-500 text-center py-8\">{emptyMessage}</p>;\n    }\n\n    return (\n      <div className=\"grid gap-4\">\n        {items.map((item: any, index: number) => (\n          <div\n            key={index}\n            className=\"p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow\"\n          >\n            <h3 className=\"font-semibold text-lg mb-2\">\n              {item.name || item.title || `项目 ${index + 1}`}\n            </h3>\n            {item.description && (\n              <p className=\"text-gray-600 mb-2\">{item.description}</p>\n            )}\n            {item.address && (\n              <p className=\"text-sm text-gray-500\">📍 {item.address}</p>\n            )}\n            {item.price && (\n              <p className=\"text-sm text-green-600\">💰 {item.price}</p>\n            )}\n            {item.rating && (\n              <p className=\"text-sm text-yellow-600\">⭐ {item.rating}</p>\n            )}\n            {typeof item === \"string\" && (\n              <p className=\"text-gray-600\">{item}</p>\n            )}\n          </div>\n        ))}\n      </div>\n    );\n  };\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case \"itinerary\":\n        return renderItinerary();\n      case \"attractions\":\n        return renderList(recData.attractions, \"暂无景点推荐\");\n      case \"restaurants\":\n        return renderList(recData.restaurants, \"暂无餐厅推荐\");\n      case \"accommodations\":\n        return renderList(recData.accommodations, \"暂无住宿建议\");\n      case \"tips\":\n        return renderList(recData.tips, \"暂无旅行贴士\");\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* AI分析 */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <h3 className=\"font-semibold text-blue-800 mb-2\">AI分析</h3>\n        <p className=\"text-blue-700\">{analysis}</p>\n      </div>\n\n      {/* 标签页 */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"flex space-x-8\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon;\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === tab.id\n                    ? \"border-blue-500 text-blue-600\"\n                    : \"border-transparent text-gray-500 hover:text-gray-700\"\n                }`}\n              >\n                <Icon className=\"w-4 h-4\" />\n                <span>{tab.label}</span>\n              </button>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* 标签页内容 */}\n      <div className=\"min-h-[300px]\">{renderTabContent()}</div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAqBe,SAAS,qBAAqB,EAC3C,IAAI,EACsB;;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,0KAAS;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,0KAAwB;IAE9D,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,qBACE,wLAAC;YAAI,WAAU;sBACb,cAAA,wLAAC;0BAAE;;;;;;;;;;;IAGT;IAEA,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG;IAEtC,cAAc;IACd,MAAM,wBAAwB;QAC5B,IAAI,OAAO,oBAAoB,UAAU;YACvC,OAAO;gBACL,SAAS;gBACT,WAAW,EAAE;gBACb,aAAa,EAAE;gBACf,aAAa,EAAE;gBACf,gBAAgB,EAAE;gBAClB,MAAM,EAAE;YACV;QACF;QAEA,OAAO;YACL,SAAS,gBAAgB,OAAO,IAAI;YACpC,WAAW,gBAAgB,SAAS,IAAI,EAAE;YAC1C,aAAa,gBAAgB,WAAW,IAAI,EAAE;YAC9C,aAAa,gBAAgB,WAAW,IAAI,EAAE;YAC9C,gBAAgB,gBAAgB,cAAc,IAAI,EAAE;YACpD,MAAM,gBAAgB,IAAI,IAAI,EAAE;QAClC;IACF;IAEA,MAAM,UAAU;IAEhB,MAAM,OAAO;QACX;YAAE,IAAI;YAAa,OAAO;YAAQ,IAAI;QAAQ;QAC9C;YAAE,IAAI;YAAe,OAAO;YAAQ,IAAI;QAAS;QACjD;YAAE,IAAI;YAAe,OAAO;YAAQ,IAAI;QAAW;QACnD;YAAE,IAAI;YAAkB,OAAO;YAAQ,IAAI;QAAM;QACjD;YAAE,IAAI;YAAQ,OAAO;YAAQ,IAAI;QAAY;KAC9C;IAED,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ,SAAS,CAAC,MAAM,EAAE;YAC7B,qBACE,wLAAC;gBAAI,WAAU;;kCACb,wLAAC;kCAAE;;;;;;oBACF,QAAQ,OAAO,kBACd,wLAAC;wBAAI,WAAU;kCACb,cAAA,wLAAC;4BAAE,WAAU;sCAAuB,QAAQ,OAAO;;;;;;;;;;;;;;;;;QAK7D;QAEA,qBACE,wLAAC;YAAI,WAAU;sBACZ,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,KAAU,sBAChC,wLAAC;oBAAgB,WAAU;;sCACzB,wLAAC;4BACC,SAAS,IACP,eAAe,gBAAgB,QAAQ,OAAO;4BAEhD,WAAU;;8CAEV,wLAAC;;sDACC,wLAAC;4CAAG,WAAU;;gDAAwB;gDAAG,QAAQ;gDAAE;;;;;;;sDACnD,wLAAC;4CAAE,WAAU;sDAAiB,IAAI,IAAI;;;;;;;;;;;;gCAEvC,gBAAgB,sBAAQ;;;;yDAAgB;;;;;;;;;;;wBAG1C,gBAAgB,uBACf,wLAAC;4BAAI,WAAU;sCACb,cAAA,wLAAC;gCAAI,WAAU;0CACZ,IAAI,UAAU,EAAE,IAAI,CAAC,UAAe,yBACnC,wLAAC;wCAAmB,WAAU;;0DAC5B,wLAAC;gDAAI,WAAU;;;;;;0DACf,wLAAC;;kEACC,wLAAC;wDAAE,WAAU;kEACV,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,WAAW,EAAE,CAAC;;;;;;kEAExC,wLAAC;wDAAE,WAAU;kEACV,SAAS,WAAW,IAAI;;;;;;;;;;;;;uCAPrB;;;;+DAYV,wLAAC;oCAAE,WAAU;8CAAiB,IAAI,WAAW,IAAI;;;;;;;;;;;;;;;;;mBA9BjD;;;;;;;;;;IAuClB;IAEA,MAAM,aAAa,CAAC,OAAc;QAChC,IAAI,CAAC,MAAM,MAAM,EAAE;YACjB,qBAAO,wLAAC;gBAAE,WAAU;0BAAkC;;;;;;QACxD;QAEA,qBACE,wLAAC;YAAI,WAAU;sBACZ,MAAM,GAAG,CAAC,CAAC,MAAW,sBACrB,wLAAC;oBAEC,WAAU;;sCAEV,wLAAC;4BAAG,WAAU;sCACX,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC;;;;;;wBAE9C,KAAK,WAAW,kBACf,wLAAC;4BAAE,WAAU;sCAAsB,KAAK,WAAW;;;;;;wBAEpD,KAAK,OAAO,kBACX,wLAAC;4BAAE,WAAU;;gCAAwB;gCAAI,KAAK,OAAO;;;;;;;wBAEtD,KAAK,KAAK,kBACT,wLAAC;4BAAE,WAAU;;gCAAyB;gCAAI,KAAK,KAAK;;;;;;;wBAErD,KAAK,MAAM,kBACV,wLAAC;4BAAE,WAAU;;gCAA0B;gCAAG,KAAK,MAAM;;;;;;;wBAEtD,OAAO,SAAS,0BACf,wLAAC;4BAAE,WAAU;sCAAiB;;;;;;;mBAnB3B;;;;;;;;;;IAyBf;IAEA,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,WAAW,QAAQ,WAAW,EAAE;YACzC,KAAK;gBACH,OAAO,WAAW,QAAQ,WAAW,EAAE;YACzC,KAAK;gBACH,OAAO,WAAW,QAAQ,cAAc,EAAE;YAC5C,KAAK;gBACH,OAAO,WAAW,QAAQ,IAAI,EAAE;YAClC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,wLAAC;QAAI,WAAU;;0BAEb,wLAAC;gBAAI,WAAU;;kCACb,wLAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,wLAAC;wBAAE,WAAU;kCAAiB;;;;;;;;;;;;0BAIhC,wLAAC;gBAAI,WAAU;0BACb,cAAA,wLAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,qBACE,wLAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,qEAAqE,EAC/E,cAAc,IAAI,EAAE,GAChB,kCACA,uDACL,CAAC;;8CAEF,wLAAC;oCAAK,WAAU;;;;;;8CAChB,wLAAC;8CAAM,IAAI,KAAK;;;;;;;2BATX,IAAI,EAAE;;;;;oBAYjB;;;;;;;;;;;0BAKJ,wLAAC;gBAAI,WAAU;0BAAiB;;;;;;;;;;;;AAGtC;GAnMwB;KAAA"}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/src/components/TravelForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { MapPin, Calendar, User, DollarSign, Heart } from \"lucide-react\";\n\ninterface TravelFormProps {\n  onSubmit: (data: any) => void;\n  loading?: boolean;\n}\n\nexport default function TravelForm({\n  onSubmit,\n  loading = false,\n}: TravelFormProps) {\n  const [formData, setFormData] = useState({\n    destination: \"\",\n    startDate: \"\",\n    endDate: \"\",\n    xiaohongshuAccount: \"\",\n    budget: \"medium\",\n    travelStyle: \"cultural\",\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!formData.destination || !formData.startDate || !formData.endDate) {\n      alert(\"请填写必要信息\");\n      return;\n    }\n\n    const submitData = {\n      destination: formData.destination,\n      travel_dates: {\n        start: formData.startDate,\n        end: formData.endDate,\n      },\n      xiaohongshu_account: formData.xiaohongshuAccount || undefined,\n      preferences: {\n        budget: formData.budget,\n        travel_style: formData.travelStyle,\n      },\n    };\n\n    onSubmit(submitData);\n  };\n\n  const handleInputChange = (\n    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>\n  ) => {\n    const { name, value } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      {/* 目的地 */}\n      <div>\n        <label className=\"flex items-center text-sm font-medium text-gray-700 mb-2\">\n          <MapPin className=\"w-4 h-4 mr-2\" />\n          旅游目的地 *\n        </label>\n        <input\n          type=\"text\"\n          name=\"destination\"\n          value={formData.destination}\n          onChange={handleInputChange}\n          placeholder=\"例如：日本东京、泰国曼谷、法国巴黎\"\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          required\n        />\n      </div>\n\n      {/* 旅行日期 */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div>\n          <label className=\"flex items-center text-sm font-medium text-gray-700 mb-2\">\n            <Calendar className=\"w-4 h-4 mr-2\" />\n            出发日期 *\n          </label>\n          <input\n            type=\"date\"\n            name=\"startDate\"\n            value={formData.startDate}\n            onChange={handleInputChange}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            required\n          />\n        </div>\n        <div>\n          <label className=\"flex items-center text-sm font-medium text-gray-700 mb-2\">\n            <Calendar className=\"w-4 h-4 mr-2\" />\n            返回日期 *\n          </label>\n          <input\n            type=\"date\"\n            name=\"endDate\"\n            value={formData.endDate}\n            onChange={handleInputChange}\n            className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            required\n          />\n        </div>\n      </div>\n\n      {/* 小红书账号 */}\n      <div>\n        <label className=\"flex items-center text-sm font-medium text-gray-700 mb-2\">\n          <User className=\"w-4 h-4 mr-2\" />\n          小红书账号 (可选)\n        </label>\n        <input\n          type=\"text\"\n          name=\"xiaohongshuAccount\"\n          value={formData.xiaohongshuAccount}\n          onChange={handleInputChange}\n          placeholder=\"输入您的小红书用户名，获取个性化推荐\"\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        />\n        <p className=\"text-xs text-gray-500 mt-1\">\n          我们将分析您的偏好，提供更精准的推荐\n        </p>\n      </div>\n\n      {/* 预算范围 */}\n      <div>\n        <label className=\"flex items-center text-sm font-medium text-gray-700 mb-2\">\n          <DollarSign className=\"w-4 h-4 mr-2\" />\n          预算范围\n        </label>\n        <select\n          name=\"budget\"\n          value={formData.budget}\n          onChange={handleInputChange}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"low\">经济型 (节约预算)</option>\n          <option value=\"medium\">中等 (平衡性价比)</option>\n          <option value=\"high\">豪华型 (追求品质)</option>\n        </select>\n      </div>\n\n      {/* 旅行风格 */}\n      <div>\n        <label className=\"flex items-center text-sm font-medium text-gray-700 mb-2\">\n          <Heart className=\"w-4 h-4 mr-2\" />\n          旅行风格\n        </label>\n        <select\n          name=\"travelStyle\"\n          value={formData.travelStyle}\n          onChange={handleInputChange}\n          className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"cultural\">文化体验</option>\n          <option value=\"adventure\">冒险探索</option>\n          <option value=\"relaxation\">休闲度假</option>\n          <option value=\"food\">美食之旅</option>\n          <option value=\"photography\">摄影打卡</option>\n          <option value=\"shopping\">购物天堂</option>\n        </select>\n      </div>\n\n      {/* 提交按钮 */}\n      <button\n        type=\"submit\"\n        disabled={loading}\n        className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-lg transition duration-200 flex items-center justify-center\"\n      >\n        {loading ? (\n          <>\n            <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"></div>\n            AI团队正在分析，请等待5到10分钟...\n          </>\n        ) : (\n          \"获取AI推荐\"\n        )}\n      </button>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAUe,SAAS,WAAW,EACjC,QAAQ,EACR,UAAU,KAAK,EACC;;IAChB,MAAM,CAAC,UAAU,YAAY,GAAG,0KAAS;QACvC,aAAa;QACb,WAAW;QACX,SAAS;QACT,oBAAoB;QACpB,QAAQ;QACR,aAAa;IACf;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,SAAS,OAAO,EAAE;YACrE,MAAM;YACN;QACF;QAEA,MAAM,aAAa;YACjB,aAAa,SAAS,WAAW;YACjC,cAAc;gBACZ,OAAO,SAAS,SAAS;gBACzB,KAAK,SAAS,OAAO;YACvB;YACA,qBAAqB,SAAS,kBAAkB,IAAI;YACpD,aAAa;gBACX,QAAQ,SAAS,MAAM;gBACvB,cAAc,SAAS,WAAW;YACpC;QACF;QAEA,SAAS;IACX;IAEA,MAAM,oBAAoB,CACxB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,qBACE,wLAAC;QAAK,UAAU;QAAc,WAAU;;0BAEtC,wLAAC;;kCACC,wLAAC;wBAAM,WAAU;;0CACf;gCAAQ,WAAU;;;;;;4BAAiB;;;;;;;kCAGrC,wLAAC;wBACC,MAAK;wBACL,MAAK;wBACL,OAAO,SAAS,WAAW;wBAC3B,UAAU;wBACV,aAAY;wBACZ,WAAU;wBACV,QAAQ;;;;;;;;;;;;0BAKZ,wLAAC;gBAAI,WAAU;;kCACb,wLAAC;;0CACC,wLAAC;gCAAM,WAAU;;kDACf;wCAAU,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,wLAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAO,SAAS,SAAS;gCACzB,UAAU;gCACV,WAAU;gCACV,QAAQ;;;;;;;;;;;;kCAGZ,wLAAC;;0CACC,wLAAC;gCAAM,WAAU;;kDACf;wCAAU,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,wLAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAO,SAAS,OAAO;gCACvB,UAAU;gCACV,WAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;;;0BAMd,wLAAC;;kCACC,wLAAC;wBAAM,WAAU;;0CACf;gCAAM,WAAU;;;;;;4BAAiB;;;;;;;kCAGnC,wLAAC;wBACC,MAAK;wBACL,MAAK;wBACL,OAAO,SAAS,kBAAkB;wBAClC,UAAU;wBACV,aAAY;wBACZ,WAAU;;;;;;kCAEZ,wLAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAM5C,wLAAC;;kCACC,wLAAC;wBAAM,WAAU;;0CACf;gCAAY,WAAU;;;;;;4BAAiB;;;;;;;kCAGzC,wLAAC;wBACC,MAAK;wBACL,OAAO,SAAS,MAAM;wBACtB,UAAU;wBACV,WAAU;;0CAEV,wLAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,wLAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,wLAAC;gCAAO,OAAM;0CAAO;;;;;;;;;;;;;;;;;;0BAKzB,wLAAC;;kCACC,wLAAC;wBAAM,WAAU;;0CACf;gCAAO,WAAU;;;;;;4BAAiB;;;;;;;kCAGpC,wLAAC;wBACC,MAAK;wBACL,OAAO,SAAS,WAAW;wBAC3B,UAAU;wBACV,WAAU;;0CAEV,wLAAC;gCAAO,OAAM;0CAAW;;;;;;0CACzB,wLAAC;gCAAO,OAAM;0CAAY;;;;;;0CAC1B,wLAAC;gCAAO,OAAM;0CAAa;;;;;;0CAC3B,wLAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,wLAAC;gCAAO,OAAM;0CAAc;;;;;;0CAC5B,wLAAC;gCAAO,OAAM;0CAAW;;;;;;;;;;;;;;;;;;0BAK7B,wLAAC;gBACC,MAAK;gBACL,UAAU;gBACV,WAAU;0BAET,wBACC;;sCACE,wLAAC;4BAAI,WAAU;;;;;;wBAAuE;;mCAIxF;;;;;;;;;;;;AAKV;GA7KwB;KAAA"}}, {"offset": {"line": 837, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 842, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport TravelForm from \"@/components/TravelForm\";\nimport RecommendationResult from \"@/components/RecommendationResult\";\nimport { MapPin, Calendar, User, Sparkles } from \"lucide-react\";\n\ninterface TravelData {\n  destination: string;\n  travel_dates: {\n    start: string;\n    end: string;\n  };\n  xiaohongshu_account?: string;\n  preferences?: {\n    budget: string;\n    travel_style: string;\n  };\n}\n\ninterface RecommendationData {\n  recommendations: any;\n  analysis: string;\n  success: boolean;\n}\n\nexport default function Home() {\n  const [recommendations, setRecommendations] =\n    useState<RecommendationData | null>(null);\n  const [statusMsg, setStatusMsg] = useState<string | null>(null);\n  const [polling, setPolling] = useState(false);\n\n  const pollForResult = async (taskId: string) => {\n    setPolling(true);\n    setStatusMsg(\"结果生成中...\");\n    let timeoutId: NodeJS.Timeout | null = null;\n    const poll = async () => {\n      try {\n        const response = await fetch(`/api/result/${taskId}`);\n        if (!response.ok) {\n          if (response.status === 404) {\n            // 任务还未创建，继续等待\n            return;\n          }\n          throw new Error(\"结果查询失败\");\n        }\n        const data = await response.json();\n        if (data.status === \"SUCCESS\") {\n          setRecommendations(data.result);\n          setStatusMsg(null);\n          setPolling(false);\n        } else if (data.status === \"FAILURE\") {\n          setStatusMsg(\"1推荐生成失败，请稍后重试\");\n          setPolling(false);\n        } else {\n          // 继续等待\n          timeoutId = setTimeout(poll, 60000); // 1分钟后再次请求\n        }\n      } catch (error) {\n        setStatusMsg(\"查询结果时出错，请稍后重试\");\n        setPolling(false);\n      }\n    };\n    poll();\n    // 最长等待15分钟\n    setTimeout(() => {\n      if (polling) {\n        setStatusMsg(\"请求超时，请稍后重试。\");\n        setPolling(false);\n        if (timeoutId) clearTimeout(timeoutId);\n      }\n    }, 900000);\n  };\n\n  const handleFormSubmit = async (data: TravelData) => {\n    setRecommendations(null);\n    setStatusMsg(\"结果生成中...\");\n    try {\n      const response = await fetch(\"/api/recommend\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      });\n      console.log(\"handleFormSubmit response\", response);\n      console.log(\"handleFormSubmit response.status\", response.status);\n      if (response.status !== 202) {\n        throw new Error(\"无法启动推荐任务\");\n      }\n      const result = await response.json();\n      console.log(\"handleFormSubmit result\", result);\n      pollForResult(result.task_id);\n    } catch (error) {\n      console.log(\"handleFormSubmit error\", error);\n      setStatusMsg(\"2推荐生成失败，请稍后重试\");\n    }\n  };\n\n  return (\n    <main className=\"container mx-auto px-4 py-8\">\n      {/* Header */}\n      <div className=\"text-center mb-12\">\n        <div className=\"flex items-center justify-center mb-4\">\n          <Sparkles className=\"w-8 h-8 text-blue-600 mr-2\" />\n          <h1 className=\"text-4xl font-bold text-gray-800\">智能旅游推荐系统</h1>\n        </div>\n        <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n          基于AI多智能体协作，为您量身定制个性化旅游推荐\n        </p>\n      </div>\n\n      {/* Features */}\n      <div className=\"grid md:grid-cols-3 gap-6 mb-12\">\n        <div className=\"bg-white rounded-lg p-6 shadow-md\">\n          <MapPin className=\"w-8 h-8 text-blue-600 mb-3\" />\n          <h3 className=\"text-lg font-semibold mb-2\">目的地专家</h3>\n          <p className=\"text-gray-600\">\n            深度分析目的地特色，提供最全面的旅游信息\n          </p>\n        </div>\n        <div className=\"bg-white rounded-lg p-6 shadow-md\">\n          <Calendar className=\"w-8 h-8 text-green-600 mb-3\" />\n          <h3 className=\"text-lg font-semibold mb-2\">智能行程规划</h3>\n          <p className=\"text-gray-600\">根据时间和偏好，制定最优化的旅行路线</p>\n        </div>\n        <div className=\"bg-white rounded-lg p-6 shadow-md\">\n          <User className=\"w-8 h-8 text-purple-600 mb-3\" />\n          <h3 className=\"text-lg font-semibold mb-2\">个性化分析</h3>\n          <p className=\"text-gray-600\">结合小红书偏好，提供专属旅游建议</p>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"grid lg:grid-cols-2 gap-8\">\n        {/* Form Section */}\n        <div className=\"bg-white rounded-xl shadow-lg p-8\">\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-6\">\n            开始您的旅程\n          </h2>\n          <TravelForm onSubmit={handleFormSubmit} />\n        </div>\n\n        {/* Results Section */}\n        <div className=\"bg-white rounded-xl shadow-lg p-8\">\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-6\">AI推荐结果</h2>\n          {statusMsg ? (\n            <div className=\"flex items-center justify-center h-64\">\n              <span className=\"ml-3 text-gray-600\">{statusMsg}</span>\n            </div>\n          ) : recommendations ? (\n            <RecommendationResult data={recommendations} />\n          ) : (\n            <div className=\"text-center text-gray-500 h-64 flex items-center justify-center\">\n              <div>\n                <Sparkles className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n                <p>填写左侧表单，获取AI个性化推荐</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;AA0Be,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GACzC,0KAAoC;IACtC,MAAM,CAAC,WAAW,aAAa,GAAG,0KAAwB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,0KAAS;IAEvC,MAAM,gBAAgB,OAAO;QAC3B,WAAW;QACX,aAAa;QACb,IAAI,YAAmC;QACvC,MAAM,OAAO;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,OAAO,CAAC;gBACpD,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,IAAI,SAAS,MAAM,KAAK,KAAK;wBAC3B,cAAc;wBACd;oBACF;oBACA,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,MAAM,KAAK,WAAW;oBAC7B,mBAAmB,KAAK,MAAM;oBAC9B,aAAa;oBACb,WAAW;gBACb,OAAO,IAAI,KAAK,MAAM,KAAK,WAAW;oBACpC,aAAa;oBACb,WAAW;gBACb,OAAO;oBACL,OAAO;oBACP,YAAY,WAAW,MAAM,QAAQ,WAAW;gBAClD;YACF,EAAE,OAAO,OAAO;gBACd,aAAa;gBACb,WAAW;YACb;QACF;QACA;QACA,WAAW;QACX,WAAW;YACT,IAAI,SAAS;gBACX,aAAa;gBACb,WAAW;gBACX,IAAI,WAAW,aAAa;YAC9B;QACF,GAAG;IACL;IAEA,MAAM,mBAAmB,OAAO;QAC9B,mBAAmB;QACnB,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,QAAQ,GAAG,CAAC,6BAA6B;YACzC,QAAQ,GAAG,CAAC,oCAAoC,SAAS,MAAM;YAC/D,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,QAAQ,GAAG,CAAC,2BAA2B;YACvC,cAAc,OAAO,OAAO;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,0BAA0B;YACtC,aAAa;QACf;IACF;IAEA,qBACE,wLAAC;QAAK,WAAU;;0BAEd,wLAAC;gBAAI,WAAU;;kCACb,wLAAC;wBAAI,WAAU;;0CACb;gCAAU,WAAU;;;;;;0CACpB,wLAAC;gCAAG,WAAU;0CAAmC;;;;;;;;;;;;kCAEnD,wLAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;0BAMzD,wLAAC;gBAAI,WAAU;;kCACb,wLAAC;wBAAI,WAAU;;0CACb;gCAAQ,WAAU;;;;;;0CAClB,wLAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,wLAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,wLAAC;wBAAI,WAAU;;0CACb;gCAAU,WAAU;;;;;;0CACpB,wLAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,wLAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,wLAAC;wBAAI,WAAU;;0CACb;gCAAM,WAAU;;;;;;0CAChB,wLAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,wLAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAKjC,wLAAC;gBAAI,WAAU;;kCAEb,wLAAC;wBAAI,WAAU;;0CACb,wLAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD;gCAAY,UAAU;;;;;;;;;;;;kCAIxB,wLAAC;wBAAI,WAAU;;0CACb,wLAAC;gCAAG,WAAU;0CAAwC;;;;;;4BACrD,0BACC,wLAAC;gCAAI,WAAU;0CACb,cAAA,wLAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;uCAEtC,gCACF;gCAAsB,MAAM;;;;;qDAE5B,wLAAC;gCAAI,WAAU;0CACb,cAAA,wLAAC;;sDACC;4CAAU,WAAU;;;;;;sDACpB,wLAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;GA1IwB;KAAA"}}, {"offset": {"line": 1187, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}