(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_next_dist_pages_f03580._.js", {

"[project]/node_modules/next/dist/pages/_app.js [client] (ecmascript, loader)": (({ r: __turbopack_require__, f: __turbopack_require_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, l: __turbopack_load__, j: __turbopack_dynamic__, p: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, g: global, __dirname }) => (() => {

__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all(["static/chunks/node_modules_next_dist_pages__app_a4b9c0.js","static/chunks/node_modules_next_dist_pages__app_4d06a8.js"].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_require__("[project]/node_modules/next/dist/pages/_app.js [client] (ecmascript, manifest chunk)");
    }).then((chunks) => {
        return Promise.all(chunks.map((chunk) => __turbopack_load__(chunk)));
    }).then(() => {
        return __turbopack_import__("[project]/node_modules/next/dist/pages/_app.js [client] (ecmascript)");
    });
});

})()),
"[project]/node_modules/next/dist/pages/_error.js [client] (ecmascript, loader)": (({ r: __turbopack_require__, f: __turbopack_require_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, l: __turbopack_load__, j: __turbopack_dynamic__, p: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, g: global, __dirname }) => (() => {

__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all(["static/chunks/node_modules_next_dist_pages__error_2701dc.js","static/chunks/node_modules_next_dist_pages__error_8a495e.js"].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_require__("[project]/node_modules/next/dist/pages/_error.js [client] (ecmascript, manifest chunk)");
    }).then((chunks) => {
        return Promise.all(chunks.map((chunk) => __turbopack_load__(chunk)));
    }).then(() => {
        return __turbopack_import__("[project]/node_modules/next/dist/pages/_error.js [client] (ecmascript)");
    });
});

})()),
}]);