{"version": 3, "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/node_modules/next/dist/shared/lib/utils/warn-once.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"warnOnce\", {\n    enumerable: true,\n    get: function() {\n        return warnOnce;\n    }\n});\nlet warnOnce = (_)=>{};\nif (process.env.NODE_ENV !== \"production\") {\n    const warnings = new Set();\n    warnOnce = (msg)=>{\n        if (!warnings.has(msg)) {\n            console.warn(msg);\n        }\n        warnings.add(msg);\n    };\n}\n\n//# sourceMappingURL=warn-once.js.map"], "names": [], "mappings": ";;AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IACzC,OAAO;AACX;AACA,OAAO,cAAc,CAAC,SAAS,YAAY;IACvC,YAAY;IACZ,KAAK;QACD,OAAO;IACX;AACJ;AACA,IAAI,WAAW,CAAC,KAAK;AACrB,wCAA2C;IACvC,MAAM,WAAW,IAAI;IACrB,WAAW,CAAC;QACR,IAAI,CAAC,SAAS,GAAG,CAAC,MAAM;YACpB,QAAQ,IAAI,CAAC;QACjB;QACA,SAAS,GAAG,CAAC;IACjB;AACJ,EAEA,qCAAqC"}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/node_modules/next/dist/shared/lib/amp-mode.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"isInAmpMode\", {\n    enumerable: true,\n    get: function() {\n        return isInAmpMode;\n    }\n});\nfunction isInAmpMode(param) {\n    let { ampFirst = false, hybrid = false, hasQuery = false } = param === void 0 ? {} : param;\n    return ampFirst || hybrid && hasQuery;\n}\n\n//# sourceMappingURL=amp-mode.js.map"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IACzC,OAAO;AACX;AACA,OAAO,cAAc,CAAC,SAAS,eAAe;IAC1C,YAAY;IACZ,KAAK;QACD,OAAO;IACX;AACJ;AACA,SAAS,YAAY,KAAK;IACtB,IAAI,EAAE,WAAW,KAAK,EAAE,SAAS,KAAK,EAAE,WAAW,KAAK,EAAE,GAAG,UAAU,KAAK,IAAI,CAAC,IAAI;IACrF,OAAO,YAAY,UAAU;AACjC,EAEA,oCAAoC"}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/node_modules/next/dist/shared/lib/amp-context.shared-runtime.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"AmpStateContext\", {\n    enumerable: true,\n    get: function() {\n        return AmpStateContext;\n    }\n});\nconst _interop_require_default = require(\"@swc/helpers/_/_interop_require_default\");\nconst _react = /*#__PURE__*/ _interop_require_default._(require(\"react\"));\nconst AmpStateContext = _react.default.createContext({});\nif (process.env.NODE_ENV !== \"production\") {\n    AmpStateContext.displayName = \"AmpStateContext\";\n}\n\n//# sourceMappingURL=amp-context.shared-runtime.js.map"], "names": [], "mappings": ";;AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IACzC,OAAO;AACX;AACA,OAAO,cAAc,CAAC,SAAS,mBAAmB;IAC9C,YAAY;IACZ,KAAK;QACD,OAAO;IACX;AACJ;AACA,MAAM,2BAA2B;AACjC,MAAM,SAAS,WAAW,GAAG,yBAAyB,CAAC,CAAC;AACxD,MAAM,kBAAkB,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;AACtD,wCAA2C;IACvC,gBAAgB,WAAW,GAAG;AAClC,EAEA,sDAAsD"}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/node_modules/next/dist/shared/lib/side-effect.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"default\", {\n    enumerable: true,\n    get: function() {\n        return SideEffect;\n    }\n});\nconst _react = require(\"react\");\nconst isServer = typeof window === \"undefined\";\nconst useClientOnlyLayoutEffect = isServer ? ()=>{} : _react.useLayoutEffect;\nconst useClientOnlyEffect = isServer ? ()=>{} : _react.useEffect;\nfunction SideEffect(props) {\n    const { headManager, reduceComponentsToState } = props;\n    function emitChange() {\n        if (headManager && headManager.mountedInstances) {\n            const headElements = _react.Children.toArray(Array.from(headManager.mountedInstances).filter(Boolean));\n            headManager.updateHead(reduceComponentsToState(headElements, props));\n        }\n    }\n    if (isServer) {\n        var _headManager_mountedInstances;\n        headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n        emitChange();\n    }\n    useClientOnlyLayoutEffect(()=>{\n        var _headManager_mountedInstances;\n        headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n        return ()=>{\n            var _headManager_mountedInstances;\n            headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.delete(props.children);\n        };\n    });\n    // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n    // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n    // being rendered, we only trigger the method from the last one.\n    // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n    // singleton in the layout effect pass, and actually trigger it in the effect pass.\n    useClientOnlyLayoutEffect(()=>{\n        if (headManager) {\n            headManager._pendingUpdate = emitChange;\n        }\n        return ()=>{\n            if (headManager) {\n                headManager._pendingUpdate = emitChange;\n            }\n        };\n    });\n    useClientOnlyEffect(()=>{\n        if (headManager && headManager._pendingUpdate) {\n            headManager._pendingUpdate();\n            headManager._pendingUpdate = null;\n        }\n        return ()=>{\n            if (headManager && headManager._pendingUpdate) {\n                headManager._pendingUpdate();\n                headManager._pendingUpdate = null;\n            }\n        };\n    });\n    return null;\n}\n\n//# sourceMappingURL=side-effect.js.map"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IACzC,OAAO;AACX;AACA,OAAO,cAAc,CAAC,SAAS,WAAW;IACtC,YAAY;IACZ,KAAK;QACD,OAAO;IACX;AACJ;AACA,MAAM,SAAS;AACf,MAAM,WAAW,OAAO,WAAW;AACnC,MAAM,4BAA4B,WAAW,KAAK,IAAI,OAAO,eAAe;AAC5E,MAAM,sBAAsB,WAAW,KAAK,IAAI,OAAO,SAAS;AAChE,SAAS,WAAW,KAAK;IACrB,MAAM,EAAE,WAAW,EAAE,uBAAuB,EAAE,GAAG;IACjD,SAAS;QACL,IAAI,eAAe,YAAY,gBAAgB,EAAE;YAC7C,MAAM,eAAe,OAAO,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,YAAY,gBAAgB,EAAE,MAAM,CAAC;YAC7F,YAAY,UAAU,CAAC,wBAAwB,cAAc;QACjE;IACJ;IACA,IAAI,UAAU;QACV,IAAI;QACJ,eAAe,OAAO,KAAK,IAAI,CAAC,gCAAgC,YAAY,gBAAgB,KAAK,OAAO,KAAK,IAAI,8BAA8B,GAAG,CAAC,MAAM,QAAQ;QACjK;IACJ;IACA,0BAA0B;QACtB,IAAI;QACJ,eAAe,OAAO,KAAK,IAAI,CAAC,gCAAgC,YAAY,gBAAgB,KAAK,OAAO,KAAK,IAAI,8BAA8B,GAAG,CAAC,MAAM,QAAQ;QACjK,OAAO;YACH,IAAI;YACJ,eAAe,OAAO,KAAK,IAAI,CAAC,gCAAgC,YAAY,gBAAgB,KAAK,OAAO,KAAK,IAAI,8BAA8B,MAAM,CAAC,MAAM,QAAQ;QACxK;IACJ;IACA,kFAAkF;IAClF,oFAAoF;IACpF,gEAAgE;IAChE,qFAAqF;IACrF,mFAAmF;IACnF,0BAA0B;QACtB,IAAI,aAAa;YACb,YAAY,cAAc,GAAG;QACjC;QACA,OAAO;YACH,IAAI,aAAa;gBACb,YAAY,cAAc,GAAG;YACjC;QACJ;IACJ;IACA,oBAAoB;QAChB,IAAI,eAAe,YAAY,cAAc,EAAE;YAC3C,YAAY,cAAc;YAC1B,YAAY,cAAc,GAAG;QACjC;QACA,OAAO;YACH,IAAI,eAAe,YAAY,cAAc,EAAE;gBAC3C,YAAY,cAAc;gBAC1B,YAAY,cAAc,GAAG;YACjC;QACJ;IACJ;IACA,OAAO;AACX,EAEA,uCAAuC"}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/node_modules/next/dist/shared/lib/head.js"], "sourcesContent": ["\"use client\";\n\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    defaultHead: null,\n    default: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    defaultHead: function() {\n        return defaultHead;\n    },\n    default: function() {\n        return _default;\n    }\n});\nconst _interop_require_default = require(\"@swc/helpers/_/_interop_require_default\");\nconst _interop_require_wildcard = require(\"@swc/helpers/_/_interop_require_wildcard\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(require(\"react\"));\nconst _sideeffect = /*#__PURE__*/ _interop_require_default._(require(\"./side-effect\"));\nconst _ampcontextsharedruntime = require(\"./amp-context.shared-runtime\");\nconst _headmanagercontextsharedruntime = require(\"./head-manager-context.shared-runtime\");\nconst _ampmode = require(\"./amp-mode\");\nconst _warnonce = require(\"./utils/warn-once\");\nfunction defaultHead(inAmpMode) {\n    if (inAmpMode === void 0) inAmpMode = false;\n    const head = [\n        /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            charSet: \"utf-8\"\n        })\n    ];\n    if (!inAmpMode) {\n        head.push(/*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width\"\n        }));\n    }\n    return head;\n}\nfunction onlyReactElement(list, child) {\n    // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n    if (typeof child === \"string\" || typeof child === \"number\") {\n        return list;\n    }\n    // Adds support for React.Fragment\n    if (child.type === _react.default.Fragment) {\n        return list.concat(// @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        _react.default.Children.toArray(child.props.children).reduce(// @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        (fragmentList, fragmentChild)=>{\n            if (typeof fragmentChild === \"string\" || typeof fragmentChild === \"number\") {\n                return fragmentList;\n            }\n            return fragmentList.concat(fragmentChild);\n        }, []));\n    }\n    return list.concat(child);\n}\nconst METATYPES = [\n    \"name\",\n    \"httpEquiv\",\n    \"charSet\",\n    \"itemProp\"\n];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/ function unique() {\n    const keys = new Set();\n    const tags = new Set();\n    const metaTypes = new Set();\n    const metaCategories = {};\n    return (h)=>{\n        let isUnique = true;\n        let hasKey = false;\n        if (h.key && typeof h.key !== \"number\" && h.key.indexOf(\"$\") > 0) {\n            hasKey = true;\n            const key = h.key.slice(h.key.indexOf(\"$\") + 1);\n            if (keys.has(key)) {\n                isUnique = false;\n            } else {\n                keys.add(key);\n            }\n        }\n        // eslint-disable-next-line default-case\n        switch(h.type){\n            case \"title\":\n            case \"base\":\n                if (tags.has(h.type)) {\n                    isUnique = false;\n                } else {\n                    tags.add(h.type);\n                }\n                break;\n            case \"meta\":\n                for(let i = 0, len = METATYPES.length; i < len; i++){\n                    const metatype = METATYPES[i];\n                    if (!h.props.hasOwnProperty(metatype)) continue;\n                    if (metatype === \"charSet\") {\n                        if (metaTypes.has(metatype)) {\n                            isUnique = false;\n                        } else {\n                            metaTypes.add(metatype);\n                        }\n                    } else {\n                        const category = h.props[metatype];\n                        const categories = metaCategories[metatype] || new Set();\n                        if ((metatype !== \"name\" || !hasKey) && categories.has(category)) {\n                            isUnique = false;\n                        } else {\n                            categories.add(category);\n                            metaCategories[metatype] = categories;\n                        }\n                    }\n                }\n                break;\n        }\n        return isUnique;\n    };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */ function reduceComponents(headChildrenElements, props) {\n    const { inAmpMode } = props;\n    return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i)=>{\n        const key = c.key || i;\n        if (process.env.NODE_ENV !== \"development\" && process.env.__NEXT_OPTIMIZE_FONTS && !inAmpMode) {\n            if (c.type === \"link\" && c.props[\"href\"] && // TODO(prateekbh@): Replace this with const from `constants` when the tree shaking works.\n            [\n                \"https://fonts.googleapis.com/css\",\n                \"https://use.typekit.net/\"\n            ].some((url)=>c.props[\"href\"].startsWith(url))) {\n                const newProps = {\n                    ...c.props || {}\n                };\n                newProps[\"data-href\"] = newProps[\"href\"];\n                newProps[\"href\"] = undefined;\n                // Add this attribute to make it easy to identify optimized tags\n                newProps[\"data-optimized-fonts\"] = true;\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            }\n        }\n        if (process.env.NODE_ENV === \"development\") {\n            // omit JSON-LD structured data snippets from the warning\n            if (c.type === \"script\" && c.props[\"type\"] !== \"application/ld+json\") {\n                const srcMessage = c.props[\"src\"] ? '<script> tag with src=\"' + c.props[\"src\"] + '\"' : \"inline <script>\";\n                (0, _warnonce.warnOnce)(\"Do not add <script> tags using next/head (see \" + srcMessage + \"). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component\");\n            } else if (c.type === \"link\" && c.props[\"rel\"] === \"stylesheet\") {\n                (0, _warnonce.warnOnce)('Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"' + c.props[\"href\"] + '\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component');\n            }\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(c, {\n            key\n        });\n    });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */ function Head(param) {\n    let { children } = param;\n    const ampState = (0, _react.useContext)(_ampcontextsharedruntime.AmpStateContext);\n    const headManager = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    return /*#__PURE__*/ _react.default.createElement(_sideeffect.default, {\n        reduceComponentsToState: reduceComponents,\n        headManager: headManager,\n        inAmpMode: (0, _ampmode.isInAmpMode)(ampState)\n    }, children);\n}\nconst _default = Head;\n\nif ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', { value: true });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}\n\n//# sourceMappingURL=head.js.map"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IACzC,OAAO;AACX;AACA,KAAK,CAAC,OAAO,OAAO,GAAG;IACnB,aAAa;IACb,SAAS;AACb,CAAC;AACD,SAAS,QAAQ,MAAM,EAAE,GAAG;IACxB,IAAI,IAAI,QAAQ,IAAI,OAAO,cAAc,CAAC,QAAQ,MAAM;QACpD,YAAY;QACZ,KAAK,GAAG,CAAC,KAAK;IAClB;AACJ;AACA,QAAQ,SAAS;IACb,aAAa;QACT,OAAO;IACX;IACA,SAAS;QACL,OAAO;IACX;AACJ;AACA,MAAM,2BAA2B;AACjC,MAAM,4BAA4B;AAClC,MAAM,SAAS,WAAW,GAAG,0BAA0B,CAAC,CAAC;AACzD,MAAM,cAAc,WAAW,GAAG,yBAAyB,CAAC,CAAC;AAC7D,MAAM,2BAA2B;AACjC,MAAM,mCAAmC;AACzC,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,SAAS,YAAY,SAAS;IAC1B,IAAI,cAAc,KAAK,GAAG,YAAY;IACtC,MAAM,OAAO;QACT,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,QAAQ;YAC/C,SAAS;QACb;KACH;IACD,IAAI,CAAC,WAAW;QACZ,KAAK,IAAI,CAAC,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,QAAQ;YACzD,MAAM;YACN,SAAS;QACb;IACJ;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,IAAI,EAAE,KAAK;IACjC,8FAA8F;IAC9F,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;QACxD,OAAO;IACX;IACA,kCAAkC;IAClC,IAAI,MAAM,IAAI,KAAK,OAAO,OAAO,CAAC,QAAQ,EAAE;QACxC,OAAO,KAAK,MAAM,CAClB,OAAO,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,QAAQ,EAAE,MAAM,CAC5D,CAAC,cAAc;YACX,IAAI,OAAO,kBAAkB,YAAY,OAAO,kBAAkB,UAAU;gBACxE,OAAO;YACX;YACA,OAAO,aAAa,MAAM,CAAC;QAC/B,GAAG,EAAE;IACT;IACA,OAAO,KAAK,MAAM,CAAC;AACvB;AACA,MAAM,YAAY;IACd;IACA;IACA;IACA;CACH;AACD;;;;AAIA,GAAG,SAAS;IACR,MAAM,OAAO,IAAI;IACjB,MAAM,OAAO,IAAI;IACjB,MAAM,YAAY,IAAI;IACtB,MAAM,iBAAiB,CAAC;IACxB,OAAO,CAAC;QACJ,IAAI,WAAW;QACf,IAAI,SAAS;QACb,IAAI,EAAE,GAAG,IAAI,OAAO,EAAE,GAAG,KAAK,YAAY,EAAE,GAAG,CAAC,OAAO,CAAC,OAAO,GAAG;YAC9D,SAAS;YACT,MAAM,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,OAAO;YAC7C,IAAI,KAAK,GAAG,CAAC,MAAM;gBACf,WAAW;YACf,OAAO;gBACH,KAAK,GAAG,CAAC;YACb;QACJ;QACA,wCAAwC;QACxC,OAAO,EAAE,IAAI;YACT,KAAK;YACL,KAAK;gBACD,IAAI,KAAK,GAAG,CAAC,EAAE,IAAI,GAAG;oBAClB,WAAW;gBACf,OAAO;oBACH,KAAK,GAAG,CAAC,EAAE,IAAI;gBACnB;gBACA;YACJ,KAAK;gBACD,IAAI,IAAI,IAAI,GAAG,MAAM,UAAU,MAAM,EAAE,IAAI,KAAK,IAAI;oBAChD,MAAM,WAAW,SAAS,CAAC,EAAE;oBAC7B,IAAI,CAAC,EAAE,KAAK,CAAC,cAAc,CAAC,WAAW;oBACvC,IAAI,aAAa,WAAW;wBACxB,IAAI,UAAU,GAAG,CAAC,WAAW;4BACzB,WAAW;wBACf,OAAO;4BACH,UAAU,GAAG,CAAC;wBAClB;oBACJ,OAAO;wBACH,MAAM,WAAW,EAAE,KAAK,CAAC,SAAS;wBAClC,MAAM,aAAa,cAAc,CAAC,SAAS,IAAI,IAAI;wBACnD,IAAI,CAAC,aAAa,UAAU,CAAC,MAAM,KAAK,WAAW,GAAG,CAAC,WAAW;4BAC9D,WAAW;wBACf,OAAO;4BACH,WAAW,GAAG,CAAC;4BACf,cAAc,CAAC,SAAS,GAAG;wBAC/B;oBACJ;gBACJ;gBACA;QACR;QACA,OAAO;IACX;AACJ;AACA;;;CAGC,GAAG,SAAS,iBAAiB,oBAAoB,EAAE,KAAK;IACrD,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,OAAO,qBAAqB,MAAM,CAAC,kBAAkB,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,YAAY,WAAW,OAAO,IAAI,MAAM,CAAC,UAAU,OAAO,GAAG,GAAG,CAAC,CAAC,GAAG;QAC3I,MAAM,MAAM,EAAE,GAAG,IAAI;QACrB;;;QAgBA,wCAA4C;YACxC,yDAAyD;YACzD,IAAI,EAAE,IAAI,KAAK,YAAY,EAAE,KAAK,CAAC,OAAO,KAAK,uBAAuB;gBAClE,MAAM,aAAa,EAAE,KAAK,CAAC,MAAM,GAAG,4BAA4B,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM;gBACtF,CAAA,GAAG,UAAU,QAAQ,AAAD,EAAG,mDAAmD,aAAa;YAC5F,OAAO,IAAI,EAAE,IAAI,KAAK,UAAU,EAAE,KAAK,CAAC,MAAM,KAAK,cAAc;gBAC5D,CAAA,GAAG,UAAU,QAAQ,AAAD,EAAG,wFAAwF,EAAE,KAAK,CAAC,OAAO,GAAG;YACtI;QACJ;QACA,OAAO,WAAW,GAAG,OAAO,OAAO,CAAC,YAAY,CAAC,GAAG;YAChD;QACJ;IACJ;AACJ;AACA;;;CAGC,GAAG,SAAS,KAAK,KAAK;IACnB,IAAI,EAAE,QAAQ,EAAE,GAAG;IACnB,MAAM,WAAW,AAAC,CAAA,GAAG,OAAO,UAAU,AAAD,EAAG,yBAAyB,eAAe;IAChF,MAAM,cAAc,AAAC,CAAA,GAAG,OAAO,UAAU,AAAD,EAAG,iCAAiC,kBAAkB;IAC9F,OAAO,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,YAAY,OAAO,EAAE;QACnE,yBAAyB;QACzB,aAAa;QACb,WAAW,AAAC,CAAA,GAAG,SAAS,WAAW,AAAD,EAAG;IACzC,GAAG;AACP;AACA,MAAM,WAAW;AAEjB,IAAI,CAAC,OAAO,QAAQ,OAAO,KAAK,cAAe,OAAO,QAAQ,OAAO,KAAK,YAAY,QAAQ,OAAO,KAAK,IAAK,KAAK,OAAO,QAAQ,OAAO,CAAC,UAAU,KAAK,aAAa;IACrK,OAAO,cAAc,CAAC,QAAQ,OAAO,EAAE,cAAc;QAAE,OAAO;IAAK;IACnE,OAAO,MAAM,CAAC,QAAQ,OAAO,EAAE;IAC/B,OAAO,OAAO,GAAG,QAAQ,OAAO;AAClC,EAEA,gCAAgC"}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/node_modules/next/dist/pages/_error.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"default\", {\n    enumerable: true,\n    get: function() {\n        return Error;\n    }\n});\nconst _interop_require_default = require(\"@swc/helpers/_/_interop_require_default\");\nconst _react = /*#__PURE__*/ _interop_require_default._(require(\"react\"));\nconst _head = /*#__PURE__*/ _interop_require_default._(require(\"../shared/lib/head\"));\nconst statusCodes = {\n    400: \"Bad Request\",\n    404: \"This page could not be found\",\n    405: \"Method Not Allowed\",\n    500: \"Internal Server Error\"\n};\nfunction _getInitialProps(param) {\n    let { res, err } = param;\n    const statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n    return {\n        statusCode\n    };\n}\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: \"100vh\",\n        textAlign: \"center\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        justifyContent: \"center\"\n    },\n    desc: {\n        lineHeight: \"48px\"\n    },\n    h1: {\n        display: \"inline-block\",\n        margin: \"0 20px 0 0\",\n        paddingRight: 23,\n        fontSize: 24,\n        fontWeight: 500,\n        verticalAlign: \"top\"\n    },\n    h2: {\n        fontSize: 14,\n        fontWeight: 400,\n        lineHeight: \"28px\"\n    },\n    wrap: {\n        display: \"inline-block\"\n    }\n};\nclass Error extends _react.default.Component {\n    render() {\n        const { statusCode, withDarkMode = true } = this.props;\n        const title = this.props.title || statusCodes[statusCode] || \"An unexpected error has occurred\";\n        return /*#__PURE__*/ _react.default.createElement(\"div\", {\n            style: styles.error\n        }, /*#__PURE__*/ _react.default.createElement(_head.default, null, /*#__PURE__*/ _react.default.createElement(\"title\", null, statusCode ? statusCode + \": \" + title : \"Application error: a client-side exception has occurred\")), /*#__PURE__*/ _react.default.createElement(\"div\", {\n            style: styles.desc\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : ''\n                }\n               */ __html: \"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}\" + (withDarkMode ? \"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\" : \"\")\n            }\n        }), statusCode ? /*#__PURE__*/ _react.default.createElement(\"h1\", {\n            className: \"next-error-h1\",\n            style: styles.h1\n        }, statusCode) : null, /*#__PURE__*/ _react.default.createElement(\"div\", {\n            style: styles.wrap\n        }, /*#__PURE__*/ _react.default.createElement(\"h2\", {\n            style: styles.h2\n        }, this.props.title || statusCode ? title : /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, \"Application error: a client-side exception has occurred (see the browser console for more information)\"), \".\"))));\n    }\n}\nError.displayName = \"ErrorPage\";\nError.getInitialProps = _getInitialProps;\nError.origGetInitialProps = _getInitialProps;\n\nif ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', { value: true });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}\n\n//# sourceMappingURL=_error.js.map"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IACzC,OAAO;AACX;AACA,OAAO,cAAc,CAAC,SAAS,WAAW;IACtC,YAAY;IACZ,KAAK;QACD,OAAO;IACX;AACJ;AACA,MAAM,2BAA2B;AACjC,MAAM,SAAS,WAAW,GAAG,yBAAyB,CAAC,CAAC;AACxD,MAAM,QAAQ,WAAW,GAAG,yBAAyB,CAAC,CAAC;AACvD,MAAM,cAAc;IAChB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACT;AACA,SAAS,iBAAiB,KAAK;IAC3B,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACnB,MAAM,aAAa,OAAO,IAAI,UAAU,GAAG,IAAI,UAAU,GAAG,MAAM,IAAI,UAAU,GAAG;IACnF,OAAO;QACH;IACJ;AACJ;AACA,MAAM,SAAS;IACX,OAAO;QACH,0FAA0F;QAC1F,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,SAAS;QACT,eAAe;QACf,YAAY;QACZ,gBAAgB;IACpB;IACA,MAAM;QACF,YAAY;IAChB;IACA,IAAI;QACA,SAAS;QACT,QAAQ;QACR,cAAc;QACd,UAAU;QACV,YAAY;QACZ,eAAe;IACnB;IACA,IAAI;QACA,UAAU;QACV,YAAY;QACZ,YAAY;IAChB;IACA,MAAM;QACF,SAAS;IACb;AACJ;AACA,MAAM,cAAc,OAAO,OAAO,CAAC,SAAS;IACxC,SAAS;QACL,MAAM,EAAE,UAAU,EAAE,eAAe,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK;QACtD,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,WAAW,CAAC,WAAW,IAAI;QAC7D,OAAO,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO;YACrD,OAAO,OAAO,KAAK;QACvB,GAAG,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,MAAM,OAAO,EAAE,MAAM,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,SAAS,MAAM,aAAa,aAAa,OAAO,QAAQ,6DAA6D,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO;YACjR,OAAO,OAAO,IAAI;QACtB,GAAG,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,SAAS;YACnD,yBAAyB;gBACrB;;;;;;;;;;;;;;;;eAgBD,GAAG,QAAQ,mGAAmG,CAAC,eAAe,oIAAoI,EAAE;YACvQ;QACJ,IAAI,aAAa,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,MAAM;YAC9D,WAAW;YACX,OAAO,OAAO,EAAE;QACpB,GAAG,cAAc,MAAM,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO;YACrE,OAAO,OAAO,IAAI;QACtB,GAAG,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,MAAM;YAChD,OAAO,OAAO,EAAE;QACpB,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,aAAa,QAAQ,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO,OAAO,CAAC,QAAQ,EAAE,MAAM,2GAA2G;IACrO;AACJ;AACA,MAAM,WAAW,GAAG;AACpB,MAAM,eAAe,GAAG;AACxB,MAAM,mBAAmB,GAAG;AAE5B,IAAI,CAAC,OAAO,QAAQ,OAAO,KAAK,cAAe,OAAO,QAAQ,OAAO,KAAK,YAAY,QAAQ,OAAO,KAAK,IAAK,KAAK,OAAO,QAAQ,OAAO,CAAC,UAAU,KAAK,aAAa;IACrK,OAAO,cAAc,CAAC,QAAQ,OAAO,EAAE,cAAc;QAAE,OAAO;IAAK;IACnE,OAAO,MAAM,CAAC,QAAQ,OAAO,EAAE;IAC/B,OAAO,OAAO,GAAG,QAAQ,OAAO;AAClC,EAEA,kCAAkC"}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}