{"version": 3, "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/node_modules/next/dist/pages/_error.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"default\", {\n    enumerable: true,\n    get: function() {\n        return Error;\n    }\n});\nconst _interop_require_default = require(\"@swc/helpers/_/_interop_require_default\");\nconst _react = /*#__PURE__*/ _interop_require_default._(require(\"react\"));\nconst _head = /*#__PURE__*/ _interop_require_default._(require(\"../shared/lib/head\"));\nconst statusCodes = {\n    400: \"Bad Request\",\n    404: \"This page could not be found\",\n    405: \"Method Not Allowed\",\n    500: \"Internal Server Error\"\n};\nfunction _getInitialProps(param) {\n    let { res, err } = param;\n    const statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n    return {\n        statusCode\n    };\n}\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: \"100vh\",\n        textAlign: \"center\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        justifyContent: \"center\"\n    },\n    desc: {\n        lineHeight: \"48px\"\n    },\n    h1: {\n        display: \"inline-block\",\n        margin: \"0 20px 0 0\",\n        paddingRight: 23,\n        fontSize: 24,\n        fontWeight: 500,\n        verticalAlign: \"top\"\n    },\n    h2: {\n        fontSize: 14,\n        fontWeight: 400,\n        lineHeight: \"28px\"\n    },\n    wrap: {\n        display: \"inline-block\"\n    }\n};\nclass Error extends _react.default.Component {\n    render() {\n        const { statusCode, withDarkMode = true } = this.props;\n        const title = this.props.title || statusCodes[statusCode] || \"An unexpected error has occurred\";\n        return /*#__PURE__*/ _react.default.createElement(\"div\", {\n            style: styles.error\n        }, /*#__PURE__*/ _react.default.createElement(_head.default, null, /*#__PURE__*/ _react.default.createElement(\"title\", null, statusCode ? statusCode + \": \" + title : \"Application error: a client-side exception has occurred\")), /*#__PURE__*/ _react.default.createElement(\"div\", {\n            style: styles.desc\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : ''\n                }\n               */ __html: \"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}\" + (withDarkMode ? \"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\" : \"\")\n            }\n        }), statusCode ? /*#__PURE__*/ _react.default.createElement(\"h1\", {\n            className: \"next-error-h1\",\n            style: styles.h1\n        }, statusCode) : null, /*#__PURE__*/ _react.default.createElement(\"div\", {\n            style: styles.wrap\n        }, /*#__PURE__*/ _react.default.createElement(\"h2\", {\n            style: styles.h2\n        }, this.props.title || statusCode ? title : /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, \"Application error: a client-side exception has occurred (see the browser console for more information)\"), \".\"))));\n    }\n}\nError.displayName = \"ErrorPage\";\nError.getInitialProps = _getInitialProps;\nError.origGetInitialProps = _getInitialProps;\n\nif ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', { value: true });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}\n\n//# sourceMappingURL=_error.js.map"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IACzC,OAAO;AACX;AACA,OAAO,cAAc,CAAC,SAAS,WAAW;IACtC,YAAY;IACZ,KAAK;QACD,OAAO;IACX;AACJ;AACA,MAAM,2BAA2B;AACjC,MAAM,SAAS,WAAW,GAAG,yBAAyB,CAAC,CAAC;AACxD,MAAM,QAAQ,WAAW,GAAG,yBAAyB,CAAC,CAAC;AACvD,MAAM,cAAc;IAChB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACT;AACA,SAAS,iBAAiB,KAAK;IAC3B,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACnB,MAAM,aAAa,OAAO,IAAI,UAAU,GAAG,IAAI,UAAU,GAAG,MAAM,IAAI,UAAU,GAAG;IACnF,OAAO;QACH;IACJ;AACJ;AACA,MAAM,SAAS;IACX,OAAO;QACH,0FAA0F;QAC1F,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,SAAS;QACT,eAAe;QACf,YAAY;QACZ,gBAAgB;IACpB;IACA,MAAM;QACF,YAAY;IAChB;IACA,IAAI;QACA,SAAS;QACT,QAAQ;QACR,cAAc;QACd,UAAU;QACV,YAAY;QACZ,eAAe;IACnB;IACA,IAAI;QACA,UAAU;QACV,YAAY;QACZ,YAAY;IAChB;IACA,MAAM;QACF,SAAS;IACb;AACJ;AACA,MAAM,cAAc,OAAO,OAAO,CAAC,SAAS;IACxC,SAAS;QACL,MAAM,EAAE,UAAU,EAAE,eAAe,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK;QACtD,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,WAAW,CAAC,WAAW,IAAI;QAC7D,OAAO,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO;YACrD,OAAO,OAAO,KAAK;QACvB,GAAG,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,MAAM,OAAO,EAAE,MAAM,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,SAAS,MAAM,aAAa,aAAa,OAAO,QAAQ,6DAA6D,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO;YACjR,OAAO,OAAO,IAAI;QACtB,GAAG,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,SAAS;YACnD,yBAAyB;gBACrB;;;;;;;;;;;;;;;;eAgBD,GAAG,QAAQ,mGAAmG,CAAC,eAAe,oIAAoI,EAAE;YACvQ;QACJ,IAAI,aAAa,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,MAAM;YAC9D,WAAW;YACX,OAAO,OAAO,EAAE;QACpB,GAAG,cAAc,MAAM,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO;YACrE,OAAO,OAAO,IAAI;QACtB,GAAG,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,MAAM;YAChD,OAAO,OAAO,EAAE;QACpB,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,aAAa,QAAQ,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO,OAAO,CAAC,QAAQ,EAAE,MAAM,2GAA2G;IACrO;AACJ;AACA,MAAM,WAAW,GAAG;AACpB,MAAM,eAAe,GAAG;AACxB,MAAM,mBAAmB,GAAG;AAE5B,IAAI,CAAC,OAAO,QAAQ,OAAO,KAAK,cAAe,OAAO,QAAQ,OAAO,KAAK,YAAY,QAAQ,OAAO,KAAK,IAAK,KAAK,OAAO,QAAQ,OAAO,CAAC,UAAU,KAAK,aAAa;IACrK,OAAO,cAAc,CAAC,QAAQ,OAAO,EAAE,cAAc;QAAE,OAAO;IAAK;IACnE,OAAO,MAAM,CAAC,QAAQ,OAAO,EAAE;IAC/B,OAAO,OAAO,GAAG,QAAQ,OAAO;AAClC,EAEA,kCAAkC"}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}