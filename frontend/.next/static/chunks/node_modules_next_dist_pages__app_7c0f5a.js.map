{"version": 3, "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/node_modules/next/dist/pages/_app.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"default\", {\n    enumerable: true,\n    get: function() {\n        return App;\n    }\n});\nconst _interop_require_default = require(\"@swc/helpers/_/_interop_require_default\");\nconst _react = /*#__PURE__*/ _interop_require_default._(require(\"react\"));\nconst _utils = require(\"../shared/lib/utils\");\n/**\n * `App` component is used for initialize of pages. It allows for overwriting and full control of the `page` initialization.\n * This allows for keeping state between navigation, custom error handling, injecting additional data.\n */ async function appGetInitialProps(param) {\n    let { Component, ctx } = param;\n    const pageProps = await (0, _utils.loadGetInitialProps)(Component, ctx);\n    return {\n        pageProps\n    };\n}\nclass App extends _react.default.Component {\n    render() {\n        const { Component, pageProps } = this.props;\n        return /*#__PURE__*/ _react.default.createElement(Component, pageProps);\n    }\n}\nApp.origGetInitialProps = appGetInitialProps;\nApp.getInitialProps = appGetInitialProps;\n\nif ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {\n  Object.defineProperty(exports.default, '__esModule', { value: true });\n  Object.assign(exports.default, exports);\n  module.exports = exports.default;\n}\n\n//# sourceMappingURL=_app.js.map"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IACzC,OAAO;AACX;AACA,OAAO,cAAc,CAAC,SAAS,WAAW;IACtC,YAAY;IACZ,KAAK;QACD,OAAO;IACX;AACJ;AACA,MAAM,2BAA2B;AACjC,MAAM,SAAS,WAAW,GAAG,yBAAyB,CAAC,CAAC;AACxD,MAAM,SAAS;AACf;;;CAGC,GAAG,eAAe,mBAAmB,KAAK;IACvC,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG;IACzB,MAAM,YAAY,MAAM,AAAC,CAAA,GAAG,OAAO,mBAAmB,AAAD,EAAG,WAAW;IACnE,OAAO;QACH;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,OAAO,CAAC,SAAS;IACtC,SAAS;QACL,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,KAAK;QAC3C,OAAO,WAAW,GAAG,OAAO,OAAO,CAAC,aAAa,CAAC,WAAW;IACjE;AACJ;AACA,IAAI,mBAAmB,GAAG;AAC1B,IAAI,eAAe,GAAG;AAEtB,IAAI,CAAC,OAAO,QAAQ,OAAO,KAAK,cAAe,OAAO,QAAQ,OAAO,KAAK,YAAY,QAAQ,OAAO,KAAK,IAAK,KAAK,OAAO,QAAQ,OAAO,CAAC,UAAU,KAAK,aAAa;IACrK,OAAO,cAAc,CAAC,QAAQ,OAAO,EAAE,cAAc;QAAE,OAAO;IAAK;IACnE,OAAO,MAAM,CAAC,QAAQ,OAAO,EAAE;IAC/B,OAAO,OAAO,GAAG,QAAQ,OAAO;AAClC,EAEA,gCAAgC"}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}