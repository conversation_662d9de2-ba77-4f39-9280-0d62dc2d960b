{"version": 3, "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/node_modules/next/dist/esm/build/templates/helpers.js"], "sourcesContent": ["/**\n * Hoists a name from a module or promised module.\n *\n * @param module the module to hoist the name from\n * @param name the name to hoist\n * @returns the value on the module (or promised module)\n */ export function hoist(module, name) {\n    // If the name is available in the module, return it.\n    if (name in module) {\n        return module[name];\n    }\n    // If a property called `then` exists, assume it's a promise and\n    // return a promise that resolves to the name.\n    if (\"then\" in module && typeof module.then === \"function\") {\n        return module.then((mod)=>hoist(mod, name));\n    }\n    // If we're trying to hoise the default export, and the module is a function,\n    // return the module itself.\n    if (typeof module === \"function\" && name === \"default\") {\n        return module;\n    }\n    // Otherwise, return undefined.\n    return undefined;\n}\n\n//# sourceMappingURL=helpers.js.map"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAAU,SAAS,MAAM,MAAM,EAAE,IAAI;IAClC,qDAAqD;IACrD,IAAI,QAAQ,QAAQ;QAChB,OAAO,MAAM,CAAC,KAAK;IACvB;IACA,gEAAgE;IAChE,8CAA8C;IAC9C,IAAI,UAAU,UAAU,OAAO,OAAO,IAAI,KAAK,YAAY;QACvD,OAAO,OAAO,IAAI,CAAC,CAAC,MAAM,MAAM,KAAK;IACzC;IACA,6EAA6E;IAC7E,4BAA4B;IAC5B,IAAI,OAAO,WAAW,cAAc,SAAS,WAAW;QACpD,OAAO;IACX;IACA,+BAA+B;IAC/B,OAAO;AACX,EAEA,mCAAmC"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/node_modules/next/dist/esm/server/future/route-kind.js"], "sourcesContent": ["export var RouteKind;\n(function(RouteKind) {\n    RouteKind[/**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */ \"PAGES\"] = \"PAGES\";\n    RouteKind[/**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */ \"PAGES_API\"] = \"PAGES_API\";\n    RouteKind[/**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */ \"APP_PAGE\"] = \"APP_PAGE\";\n    RouteKind[/**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */ \"APP_ROUTE\"] = \"APP_ROUTE\";\n})(RouteKind || (RouteKind = {}));\n\n//# sourceMappingURL=route-kind.js.map"], "names": [], "mappings": ";;;AAAO,IAAI;AACX,CAAC,SAAS,SAAS;IACf,SAAS,CAAC;;GAEX,GAAG,QAAQ,GAAG;IACb,SAAS,CAAC;;GAEX,GAAG,YAAY,GAAG;IACjB,SAAS,CAAC;;;GAGX,GAAG,WAAW,GAAG;IAChB,SAAS,CAAC;;;GAGX,GAAG,YAAY,GAAG;AACrB,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC,IAE/B,sCAAsC"}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/node_modules/next/dist/esm/server/future/route-modules/pages/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === \"edge\") {\n    module.exports = require(\"next/dist/server/future/route-modules/pages/module.js\");\n} else {\n    if (process.env.NODE_ENV === \"development\") {\n        module.exports = require(\"next/dist/compiled/next-server/pages.runtime.dev.js\");\n    } else if (process.env.TURBOPACK) {\n        module.exports = require(\"next/dist/compiled/next-server/pages-turbo.runtime.prod.js\");\n    } else {\n        module.exports = require(\"next/dist/compiled/next-server/pages.runtime.prod.js\");\n    }\n}\n\n//# sourceMappingURL=module.compiled.js.map"], "names": [], "mappings": "AAAA;;OAEO;IACH,wCAA4C;QACxC,OAAO,OAAO,GAAG;IACrB;;;AAKJ,EAEA,2CAA2C"}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["/turbopack/[project]/node_modules/next/dist/esm/build/templates/pages.js"], "sourcesContent": ["import { PagesRouteModule } from \"next/dist/esm/server/future/route-modules/pages/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/future/route-kind\";\nimport { hoist } from \"next/dist/esm/build/templates/helpers\";\n// Import the app and document modules.\nimport Document from \"@vercel/turbopack-next/pages/_document\";\nimport App from \"@vercel/turbopack-next/pages/_app\";\n// Import the userland code.\nimport * as userland from \"INNER_PAGE\";\n// Re-export the component (should be the default export).\nexport default hoist(userland, \"default\");\n// Re-export methods.\nexport const getStaticProps = hoist(userland, \"getStaticProps\");\nexport const getStaticPaths = hoist(userland, \"getStaticPaths\");\nexport const getServerSideProps = hoist(userland, \"getServerSideProps\");\nexport const config = hoist(userland, \"config\");\nexport const reportWebVitals = hoist(userland, \"reportWebVitals\");\n// Re-export legacy methods.\nexport const unstable_getStaticProps = hoist(userland, \"unstable_getStaticProps\");\nexport const unstable_getStaticPaths = hoist(userland, \"unstable_getStaticPaths\");\nexport const unstable_getStaticParams = hoist(userland, \"unstable_getStaticParams\");\nexport const unstable_getServerProps = hoist(userland, \"unstable_getServerProps\");\nexport const unstable_getServerSideProps = hoist(userland, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nexport const routeModule = new PagesRouteModule({\n    definition: {\n        kind: RouteKind.PAGES,\n        page: \"/_app\",\n        pathname: \"/_app\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App,\n        Document\n    },\n    userland\n});\n\n//# sourceMappingURL=pages.js.map\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;uCASe,sRAAgB;AAExB,MAAM,iBAAiB,sRAAgB;AACvC,MAAM,iBAAiB,sRAAgB;AACvC,MAAM,qBAAqB,sRAAgB;AAC3C,MAAM,SAAS,sRAAgB;AAC/B,MAAM,kBAAkB,sRAAgB;AAExC,MAAM,0BAA0B,sRAAgB;AAChD,MAAM,0BAA0B,sRAAgB;AAChD,MAAM,2BAA2B,sRAAgB;AACjD,MAAM,0BAA0B,sRAAgB;AAChD,MAAM,8BAA8B,sRAAgB;AAEpD,MAAM,cAAc,4NAAqB;IAC5C,YAAY;QACR,MAAM,+KAAU,KAAK;QACrB,MAAM;QACN,UAAU;QACV,2CAA2C;QAC3C,YAAY;QACZ,UAAU;IACd;IACA,YAAY;QACR,GAAG;QACH,QAAQ;IACZ;IACA,QAAQ;AACZ,IAEA,iCAAiC"}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}