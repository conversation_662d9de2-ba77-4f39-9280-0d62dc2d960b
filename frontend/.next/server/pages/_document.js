const CHUNK_PUBLIC_PATH = "server/pages/_document.js";
const runtime = require("../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/[root of the server]__224803._.js");
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/pages.js/(INNER_PAGE)/[next]/entry/pages/_document.tsx [ssr] (ecmascript) (ecmascript)", CHUNK_PUBLIC_PATH).exports;
