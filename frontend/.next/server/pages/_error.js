const CHUNK_PUBLIC_PATH = "server/pages/_error.js";
const runtime = require("../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/[root of the server]__5dd2cc._.js");
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/pages.js/(INNER_PAGE)/[next]/entry/pages/_error.tsx [ssr] (ecmascript) (ecmascript)", CHUNK_PUBLIC_PATH).exports;
