from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict, Any
import uvicorn
import os
from dotenv import load_dotenv
import uuid
from enum import Enum
import hashlib
import redis
import json
import asyncio
import time
import logging

# 加载环境变量
load_dotenv()

from agents.travel_crew import TravelRecommendationCrew

app = FastAPI(
    title="智能旅游推荐系统",
    description="基于CrewAI和DeepSeek的个性化旅游推荐API",
    version="1.0.0"
)

# CORS设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Next.js开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Redis 缓存设置 ---
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
redis_client = redis.Redis.from_url(REDIS_URL, decode_responses=True)

# --- Task Management ---
class TaskStatus(str, Enum):
    PENDING = "PENDING"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"

tasks = {}

# --- Pydantic Models ---
class TravelRequest(BaseModel):
    destination: str
    travel_dates: Dict[str, str]
    xiaohongshu_account: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None

class TaskCreationResponse(BaseModel):
    task_id: str

class TaskResultResponse(BaseModel):
    task_id: str
    status: TaskStatus
    result: Optional[Dict[str, Any]] = None

# --- Crew AI Setup ---
travel_crew = TravelRecommendationCrew()

async def run_crew_in_background(task_id: str, travel_input: Dict[str, Any]):
    try:
        # 记录任务开始时间
        start_time = time.monotonic()
        logging.info(f"开始执行任务 {task_id}，目的地: {travel_input.get('destination')}")
        
        # 添加超时控制（10分钟）
        try:
            result = await asyncio.wait_for(
                travel_crew.generate_recommendations(travel_input),
                timeout=600.0  # 10分钟超时
            )
            
            # 记录执行时间
            elapsed = time.monotonic() - start_time
            logging.info(f"任务 {task_id} 成功完成，耗时: {elapsed:.2f}秒")
            
            tasks[task_id] = {"status": TaskStatus.SUCCESS, "result": result}
            
            # 写入 Redis - 使用线程池执行同步Redis操作
            loop = asyncio.get_running_loop()
            await loop.run_in_executor(
                None,
                lambda: redis_client.hset(f"travel:task:{task_id}", mapping={
                    "status": TaskStatus.SUCCESS,
                    "result": json.dumps(result, ensure_ascii=False)
                })
            )
        except asyncio.TimeoutError:
            # 处理超时情况
            elapsed = time.monotonic() - start_time
            logging.error(f"任务 {task_id} 执行超时，已耗时: {elapsed:.2f}秒")

            error_result = {"error": "任务执行超时，请稍后再试"}
            tasks[task_id] = {"status": TaskStatus.FAILURE, "result": error_result}

            # 异步写入Redis
            await loop.run_in_executor(
                None,
                lambda: redis_client.hset(f"travel:task:{task_id}", mapping={
                    "status": TaskStatus.FAILURE,
                    "result": json.dumps(error_result, ensure_ascii=False)
                })
            )
    except Exception as e:
        import traceback
        elapsed = time.monotonic() - start_time if 'start_time' in locals() else 0
        logging.error(f"任务 {task_id} 执行失败，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        logging.error(f"异常详情: {traceback.format_exc()}")

        error_result = {"error": str(e)}
        tasks[task_id] = {"status": TaskStatus.FAILURE, "result": error_result}

        # 异步写入Redis
        try:
            loop = asyncio.get_running_loop()
            await loop.run_in_executor(
                None,
                lambda: redis_client.hset(f"travel:task:{task_id}", mapping={
                    "status": TaskStatus.FAILURE,
                    "result": json.dumps(error_result, ensure_ascii=False)
                })
            )
        except Exception as redis_error:
            logging.error(f"Redis写入失败: {redis_error}")

# --- API Endpoints ---
@app.get("/")
async def root():
    return {"message": "智能旅游推荐系统 API", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "travel-recommendation-api"}

@app.post("/api/recommend", response_model=TaskCreationResponse, status_code=202)
async def get_travel_recommendations(request: TravelRequest):
    # 1. 生成请求参数的唯一哈希
    travel_input = {
        "destination": request.destination,
        "start_date": request.travel_dates.get("start"),
        "end_date": request.travel_dates.get("end"),
        "xiaohongshu_account": request.xiaohongshu_account,
        "preferences": request.preferences or {}
    }
    input_str = json.dumps(travel_input, sort_keys=True, ensure_ascii=False)
    input_hash = hashlib.md5(input_str.encode("utf-8")).hexdigest()
    print(f"input_hash: {input_hash}")

    # 2. 查 Redis 缓存
    cached_task_id = redis_client.get(f"travel:input:{input_hash}:task_id")
    if cached_task_id:
        # 检查任务状态
        task_data = redis_client.hgetall(f"travel:task:{cached_task_id}")
        if task_data and task_data.get("status") == TaskStatus.SUCCESS:
            # 直接返回已存在的 task_id
            return TaskCreationResponse(task_id=cached_task_id)

    # 3. 没有缓存，正常生成
    task_id = str(uuid.uuid4())
    tasks[task_id] = {"status": TaskStatus.PENDING, "result": None}

    # 写入 Redis 映射
    redis_client.set(f"travel:input:{input_hash}:task_id", task_id, ex=120*3600)  # 120小时过期
    redis_client.hset(f"travel:task:{task_id}", mapping={"status": TaskStatus.PENDING, "result": ""})

    # 使用 asyncio.create_task 替代 background_tasks.add_task
    # 这样可以确保任务在后台真正异步执行，不会阻塞主请求
    asyncio.create_task(run_crew_in_background(task_id, travel_input))
    logging.info(f"任务 {task_id} 已提交到后台执行")
    return TaskCreationResponse(task_id=task_id)

@app.get("/api/result/{task_id}", response_model=TaskResultResponse)
async def get_task_result(task_id: str):
    """
    获取任务结果 - 非阻塞实现
    优先查Redis缓存，回退到内存
    """
    try:
        # 使用线程池执行Redis查询，避免阻塞事件循环
        loop = asyncio.get_running_loop()
        task_data = await loop.run_in_executor(
            None,
            lambda: redis_client.hgetall(f"travel:task:{task_id}")
        )

        if task_data:
            status = task_data.get("status", TaskStatus.PENDING)
            result = None
            if status == TaskStatus.SUCCESS and task_data.get("result"):
                try:
                    result = json.loads(task_data["result"])
                except Exception as e:
                    logging.warning(f"解析Redis结果失败: {e}")
                    result = {"error": "结果解析失败"}
            elif status == TaskStatus.FAILURE and task_data.get("result"):
                try:
                    result = json.loads(task_data["result"])
                except Exception:
                    result = {"error": "任务执行失败"}

            return TaskResultResponse(task_id=task_id, status=status, result=result)

    except Exception as e:
        logging.warning(f"Redis查询失败，回退到内存: {e}")

    # 回退到内存查询
    task = tasks.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    return TaskResultResponse(
        task_id=task_id,
        status=task["status"],
        result=task["result"]
    )

@app.post("/api/analyze-xiaohongshu")
async def analyze_xiaohongshu_account(account: str):
    """
    分析小红书账号偏好（模拟功能）
    """
    try:
        # 这里可以集成真实的小红书数据分析
        # 目前返回模拟数据
        mock_analysis = {
            "travel_style": "文艺小清新",
            "preferred_destinations": ["日本", "韩国", "台湾"],
            "budget_range": "中等",
            "interests": ["美食", "摄影", "文化体验"],
            "activity_preferences": ["博物馆", "咖啡厅", "当地市场"]
        }
        
        return {
            "account": account,
            "analysis": mock_analysis,
            "success": True
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"账号分析失败: {str(e)}")


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
