from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict, Any
import uvicorn
import uuid
from enum import Enum
import asyncio
import time
import logging
import random

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="旅游推荐系统测试版", version="1.0.0")

# CORS设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 任务状态
class TaskStatus(str, Enum):
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"

# 内存存储
tasks: Dict[str, Dict[str, Any]] = {}

# 模型
class TravelRequest(BaseModel):
    destination: str
    travel_dates: Dict[str, str]
    xiaohongshu_account: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None

class TaskCreationResponse(BaseModel):
    task_id: str
    message: str

class TaskResultResponse(BaseModel):
    task_id: str
    status: TaskStatus
    result: Optional[Dict[str, Any]] = None
    progress: Optional[str] = None
    elapsed_time: Optional[float] = None

async def simulate_ai_work(task_id: str, travel_input: Dict[str, Any]):
    """
    模拟AI工作 - 测试真正的异步执行
    """
    start_time = time.time()
    destination = travel_input.get('destination', '未知目的地')
    
    try:
        # 更新为运行状态
        tasks[task_id].update({
            "status": TaskStatus.RUNNING,
            "progress": f"正在分析{destination}的旅游信息...",
            "start_time": start_time
        })
        logger.info(f"任务 {task_id} 开始模拟AI工作")
        
        # 模拟多个阶段的工作
        stages = [
            ("分析目的地特色...", 2),
            ("查找热门景点...", 3),
            ("规划最佳路线...", 4),
            ("推荐美食餐厅...", 2),
            ("生成最终报告...", 1)
        ]
        
        for stage_name, duration in stages:
            tasks[task_id]["progress"] = stage_name
            logger.info(f"任务 {task_id}: {stage_name}")
            
            # 使用asyncio.sleep而不是time.sleep，保持异步
            await asyncio.sleep(duration)
        
        # 生成模拟结果
        mock_result = {
            "recommendations": {
                "itinerary": [
                    {
                        "day": 1,
                        "title": f"{destination}第一天",
                        "activities": [
                            "上午：抵达酒店，办理入住",
                            "下午：游览市中心",
                            "晚上：品尝当地美食"
                        ]
                    },
                    {
                        "day": 2,
                        "title": f"{destination}第二天",
                        "activities": [
                            "上午：参观著名景点",
                            "下午：购物体验",
                            "晚上：夜景观光"
                        ]
                    }
                ],
                "restaurants": [
                    {"name": f"{destination}特色餐厅A", "cuisine": "当地菜", "rating": 4.5},
                    {"name": f"{destination}网红咖啡厅", "cuisine": "咖啡甜品", "rating": 4.3}
                ],
                "attractions": [
                    {"name": f"{destination}地标建筑", "type": "历史文化", "rating": 4.8},
                    {"name": f"{destination}自然公园", "type": "自然风光", "rating": 4.6}
                ],
                "accommodations": [
                    {"name": f"{destination}精品酒店", "type": "豪华酒店", "price": "$$$$"},
                    {"name": f"{destination}青年旅社", "type": "经济住宿", "price": "$"}
                ]
            },
            "analysis": f"基于您选择的{destination}，我们为您精心规划了这份个性化旅行推荐。"
        }
        
        elapsed_time = time.time() - start_time
        
        # 更新为成功状态
        tasks[task_id].update({
            "status": TaskStatus.SUCCESS,
            "result": mock_result,
            "progress": "推荐生成完成！",
            "elapsed_time": elapsed_time
        })
        
        logger.info(f"任务 {task_id} 完成，耗时: {elapsed_time:.2f}秒")
        
    except Exception as e:
        elapsed_time = time.time() - start_time
        tasks[task_id].update({
            "status": TaskStatus.FAILURE,
            "result": {"error": str(e)},
            "progress": "执行失败",
            "elapsed_time": elapsed_time
        })
        logger.error(f"任务 {task_id} 失败: {str(e)}")

@app.get("/")
async def root():
    return {"message": "旅游推荐系统测试版", "status": "running"}

@app.post("/api/recommend", response_model=TaskCreationResponse, status_code=202)
async def get_travel_recommendations(request: TravelRequest):
    """
    提交推荐任务 - 立即返回，不阻塞
    """
    task_id = str(uuid.uuid4())
    
    travel_input = {
        "destination": request.destination,
        "start_date": request.travel_dates.get("start"),
        "end_date": request.travel_dates.get("end"),
        "xiaohongshu_account": request.xiaohongshu_account,
        "preferences": request.preferences or {}
    }
    
    # 初始化任务
    tasks[task_id] = {
        "status": TaskStatus.PENDING,
        "result": None,
        "progress": "任务已提交，等待处理...",
        "created_at": time.time()
    }
    
    # 启动后台任务
    asyncio.create_task(simulate_ai_work(task_id, travel_input))
    
    logger.info(f"任务 {task_id} 已提交，目的地: {request.destination}")
    
    return TaskCreationResponse(
        task_id=task_id,
        message=f"任务已提交，预计需要10-15秒完成"
    )

@app.get("/api/result/{task_id}", response_model=TaskResultResponse)
async def get_task_result(task_id: str):
    """
    获取任务结果 - 非阻塞查询
    """
    task = tasks.get(task_id)
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # 计算运行时间
    elapsed_time = None
    if "start_time" in task:
        elapsed_time = time.time() - task["start_time"]
    elif "created_at" in task:
        elapsed_time = time.time() - task["created_at"]
    
    return TaskResultResponse(
        task_id=task_id,
        status=task["status"],
        result=task.get("result"),
        progress=task.get("progress"),
        elapsed_time=elapsed_time
    )

@app.get("/api/tasks")
async def list_tasks():
    """
    查看所有任务状态
    """
    return {
        "total_tasks": len(tasks),
        "tasks": {
            task_id: {
                "status": task["status"],
                "progress": task.get("progress"),
                "elapsed_time": time.time() - task.get("created_at", time.time())
            }
            for task_id, task in tasks.items()
        }
    }

@app.delete("/api/tasks")
async def clear_tasks():
    """
    清空所有任务（测试用）
    """
    tasks.clear()
    return {"message": "所有任务已清空"}

if __name__ == "__main__":
    uvicorn.run(
        "main_test:app",
        host="0.0.0.0",
        port=8001,  # 使用不同端口避免冲突
        reload=True,
        log_level="info"
    )
