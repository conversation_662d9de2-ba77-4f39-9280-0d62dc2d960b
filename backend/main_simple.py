from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict, Any
import uvicorn
import os
from dotenv import load_dotenv
import uuid
from enum import Enum
import asyncio
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

from agents.travel_crew import TravelRecommendationCrew

app = FastAPI(
    title="智能旅游推荐系统",
    description="基于CrewAI和DeepSeek的个性化旅游推荐API",
    version="1.0.0"
)

# CORS设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 任务状态枚举
class TaskStatus(str, Enum):
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"

# 内存存储任务状态
tasks: Dict[str, Dict[str, Any]] = {}

# Pydantic模型
class TravelRequest(BaseModel):
    destination: str
    travel_dates: Dict[str, str]
    xiaohongshu_account: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None

class TaskCreationResponse(BaseModel):
    task_id: str
    message: str

class TaskResultResponse(BaseModel):
    task_id: str
    status: TaskStatus
    result: Optional[Dict[str, Any]] = None
    progress: Optional[str] = None
    elapsed_time: Optional[float] = None

# CrewAI实例
travel_crew = TravelRecommendationCrew()

async def run_crew_in_background(task_id: str, travel_input: Dict[str, Any]):
    """
    后台执行CrewAI任务 - 完全异步，不会阻塞API
    """
    start_time = time.time()
    
    try:
        # 更新任务状态为运行中
        tasks[task_id].update({
            "status": TaskStatus.RUNNING,
            "progress": "AI团队开始分析...",
            "start_time": start_time
        })
        logger.info(f"任务 {task_id} 开始执行，目的地: {travel_input.get('destination')}")
        
        # 设置超时保护（10分钟）
        result = await asyncio.wait_for(
            travel_crew.generate_recommendations(travel_input),
            timeout=600.0
        )
        
        # 计算执行时间
        elapsed_time = time.time() - start_time
        
        # 更新任务状态为成功
        tasks[task_id].update({
            "status": TaskStatus.SUCCESS,
            "result": result,
            "progress": "推荐生成完成！",
            "elapsed_time": elapsed_time
        })
        
        logger.info(f"任务 {task_id} 成功完成，耗时: {elapsed_time:.2f}秒")
        
    except asyncio.TimeoutError:
        elapsed_time = time.time() - start_time
        error_msg = f"任务执行超时（{elapsed_time:.1f}秒），请稍后重试"
        
        tasks[task_id].update({
            "status": TaskStatus.FAILURE,
            "result": {"error": error_msg},
            "progress": "任务超时",
            "elapsed_time": elapsed_time
        })
        
        logger.error(f"任务 {task_id} 超时，耗时: {elapsed_time:.2f}秒")
        
    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"任务执行失败: {str(e)}"
        
        tasks[task_id].update({
            "status": TaskStatus.FAILURE,
            "result": {"error": error_msg},
            "progress": "执行失败",
            "elapsed_time": elapsed_time
        })
        
        logger.error(f"任务 {task_id} 失败，耗时: {elapsed_time:.2f}秒，错误: {str(e)}")

# API端点
@app.get("/")
async def root():
    return {"message": "智能旅游推荐系统 API", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "travel-recommendation-api"}

@app.post("/api/recommend", response_model=TaskCreationResponse, status_code=202)
async def get_travel_recommendations(request: TravelRequest):
    """
    提交旅游推荐任务 - 立即返回task_id，不会阻塞
    """
    # 生成唯一任务ID
    task_id = str(uuid.uuid4())
    
    # 准备输入数据
    travel_input = {
        "destination": request.destination,
        "start_date": request.travel_dates.get("start"),
        "end_date": request.travel_dates.get("end"),
        "xiaohongshu_account": request.xiaohongshu_account,
        "preferences": request.preferences or {}
    }
    
    # 初始化任务状态
    tasks[task_id] = {
        "status": TaskStatus.PENDING,
        "result": None,
        "progress": "任务已提交，等待处理...",
        "created_at": time.time()
    }
    
    # 启动后台任务 - 使用asyncio.create_task确保真正异步
    asyncio.create_task(run_crew_in_background(task_id, travel_input))
    
    logger.info(f"任务 {task_id} 已提交，目的地: {request.destination}")
    
    return TaskCreationResponse(
        task_id=task_id,
        message="任务已提交，请使用task_id查询结果"
    )

@app.get("/api/result/{task_id}", response_model=TaskResultResponse)
async def get_task_result(task_id: str):
    """
    获取任务结果 - 非阻塞，立即返回当前状态
    """
    # 从内存中查询任务状态 - 这个操作是瞬时的，不会阻塞
    task = tasks.get(task_id)
    
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # 计算已运行时间
    elapsed_time = None
    if "start_time" in task:
        elapsed_time = time.time() - task["start_time"]
    elif "created_at" in task:
        elapsed_time = time.time() - task["created_at"]
    
    return TaskResultResponse(
        task_id=task_id,
        status=task["status"],
        result=task.get("result"),
        progress=task.get("progress"),
        elapsed_time=elapsed_time
    )

@app.get("/api/tasks")
async def list_tasks():
    """
    列出所有任务状态 - 用于调试
    """
    return {
        "total_tasks": len(tasks),
        "tasks": {
            task_id: {
                "status": task["status"],
                "progress": task.get("progress"),
                "created_at": task.get("created_at")
            }
            for task_id, task in tasks.items()
        }
    }

@app.post("/api/analyze-xiaohongshu")
async def analyze_xiaohongshu_account(account: str):
    """
    分析小红书账号偏好（模拟功能）
    """
    try:
        mock_analysis = {
            "travel_style": "文艺小清新",
            "preferred_destinations": ["日本", "韩国", "台湾"],
            "budget_range": "中等",
            "interests": ["美食", "摄影", "文化体验"],
            "activity_preferences": ["博物馆", "咖啡厅", "当地市场"]
        }
        
        return {
            "account": account,
            "analysis": mock_analysis,
            "success": True
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"账号分析失败: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
